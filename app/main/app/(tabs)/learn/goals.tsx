import React, { useState } from 'react';

import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';

import { apiPost } from '@/common/api';
import { ScrollScreen } from '@/components/base';
import { SafeAreaView } from '@/components/base/SafeAreaView';
import GoalsManager from '@/components/features/learn/GoalsManager';
import { ThemedText } from '@/components/global/ThemedText';

export default function GenerateGoals() {
  const { t } = useTranslation();
  const [confirmingGoals, setConfirmingGoals] = useState<boolean>(false);

  const handleConfirmGoals = async () => {
    setConfirmingGoals(true);
    try {
      await apiPost('/companion/goals-done', {});
      router.navigate('/main/app/(tabs)/learn/realize-goals');
    } finally {
      setConfirmingGoals(false);
    }
  };

  return (
    <SafeAreaView>
      <ScrollScreen>
        <ThemedText className="text-3xl font-bold mb-5 mx-1.5">{t('learn.goals.title')}</ThemedText>
        <TouchableOpacity onPress={() => router.navigate('/main/app/(tabs)/learn/insights')}>
          <ThemedText type="muted" className="mx-1.5 mb-2.5">
            {t('learn.goals.backLink')}
          </ThemedText>
        </TouchableOpacity>
        <ThemedText className="text-[16px] mx-1.5 mb-8">{t('learn.goals.explanation')}</ThemedText>
        <GoalsManager
          confirmButtonProps={{
            title: t('learn.goals.confirmGoals'),
            onPress: handleConfirmGoals,
            disabled: confirmingGoals,
            loading: confirmingGoals,
          }}
          parentGoalId={null}
          allowRedetermine
        />
      </ScrollScreen>
    </SafeAreaView>
  );
}
