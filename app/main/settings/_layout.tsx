import React from 'react';

import { Icon } from '@rneui/themed';
import { Slot, router, useSegments } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { Platform, TouchableOpacity, View } from 'react-native';

import { clsx } from '@/common/clsx';
import { SafeAreaView } from '@/components/base';
import { ThemedText } from '@/components/global/ThemedText';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

export default function Layout() {
  const { t } = useTranslation();
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;

  const segments = useSegments();

  const ParentView = Platform.OS === 'ios' ? View : SafeAreaView;

  return (
    <ParentView style={{ flex: 1, backgroundColor: colors.background }}>
      <View
        className="flex-row justify-between items-center"
        style={{ backgroundColor: colors.background, padding: 10, paddingTop: 15 }}
      >
        <View>
          <TouchableOpacity
            pressRetentionOffset={20}
            onPress={() => router.navigate('/main/settings')}
            className={clsx(segments.at(-1) === 'settings' && 'opacity-0 pointer-events-none')}
          >
            <View style={{ backgroundColor: colors.secondary }} className="rounded-2xl p-1 m-[5px]">
              <Icon name="arrow-back" size={16} color={colors.tabIconDefault} />
            </View>
          </TouchableOpacity>
        </View>
        <View className="flex-1 flex-row items-center justify-center">
          <ThemedText className="font-bold text-[16px] text-center">
            {t(`settings.pages.${['settings', 'plan'].includes(segments.at(-1)!) ? segments.at(-1) : 'settings'}`)}
          </ThemedText>
        </View>
        <View>
          <TouchableOpacity onPress={() => router.navigate('/main/app/learn')}>
            <View className="dark:bg-[#232223] bg-[#e0e0e0] rounded-2xl p-1 m-[5px]">
              <Icon name="close" size={16} color="#666" />
            </View>
          </TouchableOpacity>
        </View>
      </View>
      <View className="flex-1">
        <Slot screenOptions={{ headerShown: false }} />
      </View>
    </ParentView>
  );
}
