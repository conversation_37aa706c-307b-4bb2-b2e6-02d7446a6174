import React from 'react';

import { LinearGradient } from 'expo-linear-gradient';
import { StyleSheet, TouchableOpacity, View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

interface ThemedCardProps {
  title: string;
  subtitle?: string;
  onPress?: () => void;
  children?: React.ReactNode;
  variant?: 'default' | 'gradient';
}

export function ThemedCard({ title, subtitle, onPress, children, variant = 'default' }: ThemedCardProps) {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;
  const gradientColors = colors.linearGradient;

  const gradientContent = (
    <LinearGradient
      colors={gradientColors}
      style={[styles.card, styles.gradientCard]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <ThemedText
        size={18}
        type="bold"
        lightColor={colors.background}
        darkColor={colors.background}
        style={styles.title}
      >
        {title}
      </ThemedText>
      {subtitle && (
        <ThemedText
          size={14}
          lightColor={colors.background}
          darkColor={colors.background}
          style={[styles.subtitle, { opacity: 0.8 }]}
        >
          {subtitle}
        </ThemedText>
      )}
      {children}
    </LinearGradient>
  );

  const defaultContent = (
    <View
      style={[
        styles.card,
        {
          backgroundColor: colors.background,
          borderColor: colors.border,
        },
      ]}
    >
      <ThemedText size={18} type="bold" style={styles.title}>
        {title}
      </ThemedText>
      {subtitle && (
        <ThemedText size={14} style={[styles.subtitle, { opacity: 0.7 }]}>
          {subtitle}
        </ThemedText>
      )}
      {children}
    </View>
  );

  const content = variant === 'gradient' ? gradientContent : defaultContent;

  if (onPress) {
    return (
      <TouchableOpacity onPress={onPress} style={styles.container}>
        {content}
      </TouchableOpacity>
    );
  }

  return <View style={styles.container}>{content}</View>;
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  card: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  gradientCard: {
    borderWidth: 0,
  },
  title: {
    marginBottom: 4,
  },
  subtitle: {
    marginBottom: 8,
  },
});
