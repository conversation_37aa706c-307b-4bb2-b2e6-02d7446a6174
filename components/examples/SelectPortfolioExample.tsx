import React, { useState } from 'react';

import { View } from 'react-native';

import { SelectPortfolio } from '@/components/features/portfolio-overview';
import { ThemedText } from '@/components/global/ThemedText';
import { UserProfileDepot } from '@/types/depot.types';

export const SelectPortfolioExample = () => {
  const [selectedPortfolios, setSelectedPortfolios] = useState<UserProfileDepot[]>([]);
  const [singlePortfolio, setSinglePortfolio] = useState<UserProfileDepot[]>([]);

  return (
    <View className="p-4 gap-8">
      <ThemedText h1 className="mb-5">
        Select Portfolio Examples
      </ThemedText>

      {/* Multiple Selection Example */}
      <View>
        <ThemedText className="mb-2 font-medium">Multiple Portfolio Selection</ThemedText>
        <SelectPortfolio
          selectedPortfolios={selectedPortfolios}
          onSelect={setSelectedPortfolios}
          multiple={true}
          placeholder="Select multiple portfolios"
        />

        {selectedPortfolios.length > 0 && (
          <View className="mt-2 p-3 bg-blue-50 dark:bg-blue-900 rounded-lg">
            <ThemedText className="font-medium mb-1">Selected Portfolios:</ThemedText>
            {selectedPortfolios.map(portfolio => (
              <View key={portfolio.id} className="flex-row justify-between py-1">
                <ThemedText>{portfolio.bankName}</ThemedText>
                <ThemedText className="text-gray-500">{portfolio.number}</ThemedText>
              </View>
            ))}
          </View>
        )}
      </View>

      {/* Single Selection Example */}
      <View>
        <ThemedText className="mb-2 font-medium">Single Portfolio Selection</ThemedText>
        <SelectPortfolio
          selectedPortfolios={singlePortfolio}
          onSelect={setSinglePortfolio}
          multiple={false}
          placeholder="Select one portfolio"
        />

        {singlePortfolio.length > 0 && (
          <View className="mt-2 p-3 bg-green-50 dark:bg-green-900 rounded-lg">
            <ThemedText className="font-medium mb-1">Selected Portfolio:</ThemedText>
            <ThemedText>
              {singlePortfolio[0].bankName} - {singlePortfolio[0].number}
            </ThemedText>
          </View>
        )}
      </View>
    </View>
  );
};
