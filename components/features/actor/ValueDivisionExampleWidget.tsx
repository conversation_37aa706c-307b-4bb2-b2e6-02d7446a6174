import React from 'react';

import { View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

import ExpandableWidget from './ExpandableWidget';

// Theme-aware color palette for data visualization
const getSegmentColors = (colors: any) => [
  colors.primary, // Use primary theme color
  '#078566', // Green
  '#E97633', // Orange
  '#C84279', // Red/Pink
];

// Mock data for the example - similar to the image you showed
const getMockData = (colors: any) => ({
  totalValue: 6300000, // $6.3M
  segments: [
    { label: 'Technology', value: 2520000, percentage: 40, color: getSegmentColors(colors)[0] },
    { label: 'Healthcare', value: 1890000, percentage: 30, color: getSegmentColors(colors)[1] },
    { label: 'Finance', value: 1260000, percentage: 20, color: getSegmentColors(colors)[2] },
    { label: 'Energy', value: 630000, percentage: 10, color: getSegmentColors(colors)[3] },
  ],
  additionalDetails: [
    { category: 'Growth Stocks', value: 3780000, description: 'High-growth technology and healthcare companies' },
    { category: 'Value Stocks', value: 1890000, description: 'Undervalued financial and energy companies' },
    { category: 'Dividend Stocks', value: 630000, description: 'Stable dividend-paying companies' },
    { category: 'International', value: 1260000, description: 'Global diversification across markets' },
    { category: 'Bonds', value: 315000, description: 'Government and corporate bonds' },
    { category: 'Cash', value: 315000, description: 'Cash and cash equivalents' },
  ],
});

const CircularProgress = ({
  percentage,
  color,
  size = 120,
  totalValue,
}: {
  percentage: number;
  color: string;
  size?: number;
  totalValue: number;
}) => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;

  return (
    <View
      className="justify-center items-center rounded-full"
      style={{
        width: size,
        height: size,
        backgroundColor: color + '20',
        borderWidth: 8,
        borderColor: color,
      }}
    >
      <ThemedText size={24} type="bold" style={{ color: colors.text }}>
        ${(totalValue / 1000000).toFixed(1)}M
      </ThemedText>
    </View>
  );
};

const PreviewContent = () => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;
  const mockData = getMockData(colors);

  return (
    <View className="items-center">
      <CircularProgress percentage={100} color={colors.primary} totalValue={mockData.totalValue} />

      <View className="mt-4 w-full">
        {mockData.segments.slice(0, 2).map((segment: any, index: number) => (
          <View key={index} className="flex-row items-center justify-between py-2">
            <View className="flex-row items-center flex-1">
              <View className="w-3 h-3 rounded-full mr-3" style={{ backgroundColor: segment.color }} />
              <ThemedText className="flex-1" style={{ color: colors.text }}>
                {segment.label}
              </ThemedText>
            </View>
            <ThemedText className="font-semibold" style={{ color: colors.text }}>
              {segment.percentage}%
            </ThemedText>
          </View>
        ))}
        <ThemedText className="text-center text-muted mt-2">+{mockData.segments.length - 2} more categories</ThemedText>
      </View>
    </View>
  );
};

const FullContent = () => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;
  const mockData = getMockData(colors);

  return (
    <View>
      {/* Main Chart */}
      <View className="items-center mb-6">
        <CircularProgress percentage={100} color={colors.primary} size={160} totalValue={mockData.totalValue} />
      </View>

      {/* All Segments */}
      <View className="mb-6">
        <ThemedText h3 type="bold" className="mb-4">
          Portfolio Breakdown
        </ThemedText>
        {mockData.segments.map((segment: any, index: number) => (
          <View
            key={index}
            className="flex-row items-center justify-between py-3 border-b"
            style={{ borderBottomColor: colors.border }}
          >
            <View className="flex-row items-center flex-1">
              <View className="w-4 h-4 rounded-full mr-3" style={{ backgroundColor: segment.color }} />
              <View className="flex-1">
                <ThemedText type="regular">{segment.label}</ThemedText>
                <ThemedText size={14} textType="muted">
                  ${(segment.value / 1000000).toFixed(1)}M
                </ThemedText>
              </View>
            </View>
            <ThemedText type="semi-bold" size={18}>
              {segment.percentage}%
            </ThemedText>
          </View>
        ))}
      </View>

      {/* Additional Details */}
      <View>
        <ThemedText h3 type="bold" className="mb-4">
          Detailed Analysis
        </ThemedText>
        {mockData.additionalDetails.map((detail: any, index: number) => (
          <View key={index} className="py-3 border-b" style={{ borderBottomColor: colors.border }}>
            <View className="flex-row justify-between items-center mb-1">
              <ThemedText type="regular">{detail.category}</ThemedText>
              <ThemedText type="semi-bold">${(detail.value / 1000000).toFixed(1)}M</ThemedText>
            </View>
            <ThemedText size={14} textType="muted">
              {detail.description}
            </ThemedText>
          </View>
        ))}
      </View>
    </View>
  );
};

export default function ValueDivisionExampleWidget() {
  return (
    <ExpandableWidget
      title="Value Division"
      subtitle="Touch chart to view info"
      ready={true}
      expandedContent={<FullContent />}
    >
      <PreviewContent />
    </ExpandableWidget>
  );
}
