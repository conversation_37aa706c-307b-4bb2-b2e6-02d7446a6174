import React, { useEffect, useMemo, useState } from 'react';

import { Calendar, DateData, LocaleConfig } from '@divizend/react-native-calendars';
import dayjs, { Dayjs } from 'dayjs';
import { useTranslation } from 'react-i18next';

import { getDaysInMonthForCalendar } from '@/common/date-helper';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';
import usePortfolioQuery from '@/hooks/actor/useDepotQuery';
import { ActorService } from '@/services/actor.service';
import { ActorState, actor } from '@/signals/actor';
import { CalendarDay } from '@/types/actor-api.types';
import { rgbToHsl } from '@/utils/strings';

import Widget from '../Widget';
import CalendarEntryModal from './CalendarEntryModal';
import { CalendarDayEvent, CalendarDayEventType, EventsCalendarDay } from './common.types';

function getCalendarEvents(
  depot: ActorState['depot'],
  dividends: CalendarDay[],
  calendarDays: dayjs.Dayjs[],
  showOnlyMyDividends: boolean,
): EventsCalendarDay[] {
  return calendarDays.map(date => {
    const isoDate = date.format('YYYY-MM-DD');

    const relevantDate = dividends.find(date => date.date === isoDate);
    const relevantDividends_ = (
      (relevantDate === undefined ? [] : relevantDate.dividends).map(dividend => ({
        type: CalendarDayEventType.DIVIDEND,
        dividend,
        isin: dividend.isin,
        name: dividend.company.name,
      })) as CalendarDayEvent[]
    ).filter(event => !showOnlyMyDividends || event.isin in (depot?.securities ?? {}));

    // Remove redundant dividends
    const relevantDividends: CalendarDayEvent[] = Object.values(
      relevantDividends_.reduce(
        (acc: any, event: any) => {
          if (acc[event.isin] === undefined) {
            acc[event.isin] = event;
          } else if (acc[event.isin].dividend.amount !== event.dividend.amount) {
            acc[event.isin + event.dividend.amount] = event;
          }
          return acc;
        },
        {} as Record<string, CalendarDayEvent>,
      ),
    );

    const relavantTransactionsAndSplits: CalendarDayEvent[] = Object.keys(depot?.securities ?? {})
      .map(isin => {
        const transactions: CalendarDayEvent[] = (depot?.securities[isin].transactions ?? [])
          .filter(transaction => new Date(transaction.date).toISOString().slice(0, 10) === isoDate)
          .map(transaction => ({
            type: CalendarDayEventType.TRANSACTION,
            transaction,
            isin,
            name: depot?.securities[isin].name ?? '',
          }));
        const splits: CalendarDayEvent[] = (depot?.securities[isin].splits ?? [])
          .filter(split => new Date(split.date).toISOString().slice(0, 10) === isoDate)
          .map(split => ({
            type: CalendarDayEventType.SPLIT,
            split,
            isin,
            name: depot?.securities[isin].name ?? '',
          }));

        return [...transactions, ...splits];
      })
      .flat();

    return {
      date,
      events: [...relevantDividends, ...relavantTransactionsAndSplits].sort((a, b) => a.name.localeCompare(b.name)),
    };
  });
}

const useChangeSyncCalendarLanguage = () => {
  const { i18n } = useTranslation();

  useEffect(() => {
    const langShort = i18n.language.split('-')[0];
    const dateObj = i18n.t('date', { returnObjects: true });
    if (Object.keys(dateObj).length > 0) {
      LocaleConfig.locales[langShort] = dateObj;
      LocaleConfig.defaultLocale = langShort;
    } else {
      LocaleConfig.locales['en'] = LocaleConfig.locales['en'] ?? {};
      LocaleConfig.defaultLocale = 'en';
    }
  }, [i18n.language]);
};

export default function CalendarWidget() {
  useChangeSyncCalendarLanguage();

  const { t } = useTranslation();
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;
  const depot = actor.value.depot;
  const [selectedDay, setSelectedDay] = useState<Dayjs | null>(null);
  const [currentYear, setCurrentYear] = useState(dayjs().year());
  const [currentMonth, setCurrentMonth] = useState(dayjs().month() + 1);
  const { data: { entries: dividends_ = [] } = { entries: [] }, isLoading } = usePortfolioQuery({
    queryFn: () => ActorService.getDividendCalendar(currentYear, currentMonth),
    queryKey: ['getDividendCalendar', currentYear, currentMonth],
  });
  const { data: selectedCalendarDayDetails } = usePortfolioQuery({
    queryFn: () =>
      selectedDay
        ? ActorService.getCalendarDayDetails(dividends_.find(calendarDay => selectedDay.isSame(calendarDay.date))!)
        : null,
    queryKey: ['getCalendarDayDetails', selectedDay?.toISOString(), currentYear, currentMonth],
  });

  const dividends = useMemo(() => {
    if (!selectedCalendarDayDetails?.calendarDay) return dividends_;

    return dividends_.map(dividend => {
      if (dividend.date === selectedCalendarDayDetails.calendarDay.date) {
        return selectedCalendarDayDetails.calendarDay;
      }
      return dividend;
    });
  }, [dividends_, selectedCalendarDayDetails]);

  const calendarDays = useMemo(() => getDaysInMonthForCalendar(currentYear, currentMonth), [currentYear, currentMonth]);
  const dailyEvents: EventsCalendarDay[] = useMemo(() => {
    const events = getCalendarEvents(depot, dividends, calendarDays, false);
    return events;
  }, [dividends, depot, calendarDays]);

  const markedDates = useMemo(() => {
    const marks: Record<string, any> = {};
    const maxEvents = Math.max(...dailyEvents.map(event => event.events.length), 0);
    const baseHighest = 'rgb(46, 120, 119)';
    const baseLightest = 'rgb(154, 216, 215)';
    const hslBase = rgbToHsl(baseHighest);
    const hslLowest = rgbToHsl(baseLightest);
    dailyEvents.forEach(event => {
      if (event.events.length > 0) {
        const ratio = 1 - Math.pow(event.events.length / maxEvents, 0.25);
        const hsl = { ...hslBase };
        hsl.l = (hslLowest.l - hslBase.l) * ratio + hslBase.l;
        marks[event.date.format('YYYY-MM-DD')] = {
          marked: true,
          dotColor: hsl.toString(),
        };
      }
    });
    if (selectedDay) {
      marks[selectedDay.format('YYYY-MM-DD')] = {
        ...marks[selectedDay.format('YYYY-MM-DD')],
        selected: true,
        disableTouchEvent: true,
      };
    }
    return marks;
  }, [dailyEvents, selectedDay]);

  const selectedDayDetails = useMemo(() => {
    if (!selectedDay) return null;
    return dailyEvents.find(event => event.date.isSame(selectedDay, 'day'));
  }, [dailyEvents, selectedDay, selectedCalendarDayDetails]);

  return (
    <Widget title={t('actor.calendarWidget.title')} ready>
      <Calendar
        hideWeekends
        markedDates={markedDates}
        displayLoadingIndicator={isLoading}
        onDayPress={async (day: DateData) => {
          const selectedDate = dayjs(day.dateString).startOf('day');
          setSelectedDay(selectedDate);
        }}
        onMonthChange={(date: DateData) => {
          const dateDayjs = dayjs(date.dateString);
          if (dateDayjs.year() === currentYear && dateDayjs.month() + 1 === currentMonth) return;
          setCurrentYear(dateDayjs.year());
          setCurrentMonth(dateDayjs.month() + 1);
          setSelectedDay(null);
        }}
        theme={{
          'stylesheet.calendar.header': Object.fromEntries(
            Array.from({ length: 7 }, (_, i) => [`dayTextAtIndex${i}`, { color: colors.text, fontSize: 14 }]),
          ),
          arrowColor: 'rgb(46, 120, 119)',
          monthTextColor: colors.text,
          todayTextColor: colors.text,
          todayBackgroundColor: 'rgb(154, 216, 215)',
          selectedDayTextColor: 'rgb(142, 120, 119)',
          selectedDayBackgroundColor: 'rgb(154, 216, 215)',
          textDayFontWeight: '400',
          textMonthFontWeight: '500',
          calendarBackground: 'transparent',
          dayTextColor: colors.text,
          textDisabledColor: colors.tabIconDefault,
        }}
      />

      {/* Calendar Entry Modal */}
      {selectedDayDetails && (
        <CalendarEntryModal
          calendarDay={selectedDayDetails}
          visible={!!selectedDay}
          onClose={() => setSelectedDay(null)}
        />
      )}
    </Widget>
  );
}
