import { useEffect } from 'react';

import { Pie<PERSON>hart as PieChartBase, pieDataItem } from 'react-native-gifted-charts';

import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

export interface PieDataItem extends pieDataItem {
  id: string;
}

interface PieChartProps<T> {
  data: T[];
  centerLabelComponent: () => JSX.Element;
  selectedSegment: T | null;
  setSelectedSegment: (segment: T | null) => void;
}

export default function PieChart<Datum extends pieDataItem>({
  data,
  centerLabelComponent,
  selectedSegment,
  setSelectedSegment,
}: PieChartProps<Datum>) {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;

  const updateFocus = (selected: Datum | null) => {
    data.forEach(segment => {
      segment.focused = selected ? segment === selected : false;
    });
  };

  useEffect(() => {
    updateFocus(selectedSegment);
  }, [selectedSegment]);

  return (
    <PieChartBase
      data={data}
      showGradient
      extraRadius={7}
      focusOnPress
      inwardExtraLengthForFocused={3}
      animationDuration={500}
      radius={130}
      innerRadius={90}
      strokeWidth={1.5}
      strokeColor={colors.secondary}
      innerCircleColor={colors.secondary}
      focusedPieIndex={selectedSegment ? data.indexOf(selectedSegment) : -1}
      centerLabelComponent={centerLabelComponent}
      onPress={(segment: Datum) => setSelectedSegment(segment?.focused ? null : (segment ?? null))}
    />
  );
}
