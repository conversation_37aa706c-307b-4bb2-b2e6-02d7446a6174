import React from 'react';

import { Icon } from '@rneui/themed';
import { Pressable, ScrollView, View } from 'react-native';

import { SafeAreaView } from '@/components/base';
import { ThemedText } from '@/components/global/ThemedText';
import { ModalManager } from '@/components/global/modal';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

interface ExpandableWidgetProps {
  title?: string;
  children: React.ReactNode;
  ready?: boolean;
  subtitle?: string;
  expandedContent?: React.ReactNode;
}

interface ExpandableWidgetModalProps {
  title?: string;
  children?: React.ReactNode;
  subtitle?: string;
  expandedContent?: React.ReactNode;
  dismiss: () => void;
}

const ExpandableWidgetModal: React.FC<ExpandableWidgetModalProps> = ({
  title,
  children,
  subtitle,
  expandedContent,
  dismiss,
}) => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;

  return (
    <SafeAreaView className="flex-1" style={{ backgroundColor: colors.background }}>
      {/* Header without border in modal */}
      <View
        className="px-4 pt-4 pb-4"
        style={{
          backgroundColor: colors.secondary,
        }}
      >
        <View className="flex-row justify-between items-center">
          <View className="flex-1">
            {title && (
              <ThemedText h2 type="bold" className="mb-1">
                {title}
              </ThemedText>
            )}
            {subtitle && <ThemedText textType="muted">{subtitle}</ThemedText>}
          </View>
          <Pressable onPress={dismiss} className="p-2 rounded-full" style={{ backgroundColor: colors.background }}>
            <Icon name="close" type="material" size={24} color={colors.text} />
          </Pressable>
        </View>
      </View>

      {/* Content */}
      <ScrollView className="flex-1 p-4">{expandedContent || children}</ScrollView>
    </SafeAreaView>
  );
};

export default function ExpandableWidget({
  title,
  children,
  ready = true,
  subtitle,
  expandedContent,
}: ExpandableWidgetProps) {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;

  const handleExpand = () => {
    ModalManager.showModal(
      ExpandableWidgetModal,
      {
        title,
        children,
        subtitle,
        expandedContent,
      },
      {
        presentationStyle: 'fullScreen',
        animationType: 'slide',
      },
    );
  };

  if (!ready) {
    return (
      <View
        className="rounded-xl p-4 mb-6 min-h-[200px] justify-center items-center"
        style={{
          backgroundColor: colors.secondary,
          borderTopWidth: 4,
          borderTopColor: colors.primary,
        }}
      >
        <ThemedText>Loading...</ThemedText>
      </View>
    );
  }

  return (
    <View
      className="rounded-xl mb-6 overflow-hidden"
      style={{
        backgroundColor: colors.secondary,
        borderTopWidth: 4,
        borderTopColor: colors.primary,
      }}
    >
      {/* Header */}
      {(title || subtitle) && (
        <View className="px-4 pt-4 pb-2">
          {title && (
            <ThemedText h3 type="bold" className="mb-1">
              {title}
            </ThemedText>
          )}
          {subtitle && (
            <ThemedText textType="muted" size={14}>
              {subtitle}
            </ThemedText>
          )}
        </View>
      )}

      {/* Content - with limited height to show preview */}
      <View className="px-4 pb-4 max-h-[300px] overflow-hidden">{children}</View>

      {/* Expand button at bottom */}
      <Pressable
        onPress={handleExpand}
        className="flex-row justify-center items-center py-3 border-t"
        style={{
          borderTopColor: colors.tabIconDefault,
          backgroundColor: colors.background + '20', // slight transparency
        }}
      >
        <View className="rounded-full p-2" style={{ backgroundColor: colors.primary }}>
          <Icon name="fullscreen" type="material" size={20} color="white" />
        </View>
      </Pressable>
    </View>
  );
}
