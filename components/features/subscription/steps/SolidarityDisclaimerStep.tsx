import { ScrollView, View } from 'react-native';

import Accordion from '@/components/base/Accordion';
import { ThemedText } from '@/components/global/ThemedText';
import { t as tBase } from '@/i18n';

import { IStepProps } from '../SubscriptionModal';

export default function SolidarityDisclaimerStep({}: IStepProps) {
  const t = (key: string, data?: any) => tBase('subscription.choosePlan.steps.disclaimer.' + key, data);

  return (
    <ScrollView className="flex p-4 mb-2">
      <ThemedText className="mb-4 text-center" id="title" h2>
        {t('title')}
      </ThemedText>
      <ThemedText className="mb-4 px-5" id="description">
        {t('description')}
      </ThemedText>

      <Accordion
        className="mb-4"
        title={t('accordion.spotsContributions.title')}
        initiallyOpen
        unitedBackground
        content={
          <View className="py-3 px-5">
            <ThemedText className="font-semibold">
              {t('accordion.spotsContributions.content.sponsors.title')}
            </ThemedText>
            <ThemedText className="mb-3">
              {' '}
              {`\u2022`} {t('accordion.spotsContributions.content.sponsors.bullet1')}
            </ThemedText>
            <ThemedText className="mb-3">
              {' '}
              {`\u2022`} {t('accordion.spotsContributions.content.sponsors.bullet2')}
            </ThemedText>
            <ThemedText className="mb-3">
              {' '}
              {`\u2022`} {t('accordion.spotsContributions.content.sponsors.bullet3')}
            </ThemedText>

            <ThemedText className="font-semibold">
              {t('accordion.spotsContributions.content.sponsored.title')}
            </ThemedText>
            <ThemedText className="mb-3">
              {' '}
              {`\u2022`} {t('accordion.spotsContributions.content.sponsored.bullet1')}
            </ThemedText>
            <ThemedText className="mb-3">
              {' '}
              {`\u2022`} {t('accordion.spotsContributions.content.sponsored.bullet2')}
            </ThemedText>
          </View>
        }
      />

      <Accordion
        className="mb-4"
        title={t('accordion.waitlist.title')}
        unitedBackground
        initiallyOpen
        content={
          <View className="py-3 px-5">
            <ThemedText className="mb-3">
              {' '}
              {`\u2022`} {t('accordion.waitlist.content.bullet1')}
            </ThemedText>
            <ThemedText className="mb-3">
              {' '}
              {`\u2022`} {t('accordion.waitlist.content.bullet2')}
            </ThemedText>
            <ThemedText className="mb-3">
              {' '}
              {`\u2022`} {t('accordion.waitlist.content.bullet3')}
            </ThemedText>
          </View>
        }
      />

      <Accordion
        className="mb-4"
        title={t('accordion.changes.title')}
        unitedBackground
        initiallyOpen
        content={
          <View className="py-3 px-5">
            <ThemedText className="mb-3">
              {' '}
              {`\u2022`} {t('accordion.changes.content.bullet1')}
            </ThemedText>
            <ThemedText className="mb-3">
              {' '}
              {`\u2022`} {t('accordion.changes.content.bullet2')}
            </ThemedText>
            <ThemedText className="font-semibold">{t('accordion.changes.content.note')}</ThemedText>
            <ThemedText className="mb-3">
              {' '}
              {`\u2022`} {t('accordion.changes.content.bullet3')}
            </ThemedText>
          </View>
        }
      />
    </ScrollView>
  );
}
