import React from 'react';

import { View } from 'react-native';
import { ScrollView } from 'react-native';

import Accordion from '@/components/base/Accordion';
import { ThemedText } from '@/components/global/ThemedText';
import { t as tBase } from '@/i18n';

type Props = {};

export default function JoinedWaitlistStep({}: Props) {
  const t = (key: string, data?: any) => tBase('subscription.choosePlan.steps.waitlist.' + key, data);

  return (
    <View className="flex-1">
      <ScrollView className="flex p-4 mb-2">
        <ThemedText className="mb-4 text-center" id="title" h2>
          {t('title')}
        </ThemedText>
        <ThemedText className="mb-4 px-5" id="description">
          {t('description')}
        </ThemedText>
        <Accordion
          unitedBackground
          initiallyOpen
          title={t('accordion.title')}
          content={
            <View className="py-3 px-5 gap-2">
              <ThemedText className="font-semibold">{t('accordion.spotMatching.title')}</ThemedText>
              <ThemedText className="ml-2">
                {`\u2022`} {t('accordion.spotMatching.point1')}
              </ThemedText>
              <ThemedText className="ml-2">
                {`\u2022`} {t('accordion.spotMatching.point2')}
              </ThemedText>
              <ThemedText className="font-semibold">{t('accordion.notification.title')}</ThemedText>
              <ThemedText className="ml-2">
                {`\u2022`} {t('accordion.notification.point1')}
              </ThemedText>
              <ThemedText className="ml-2">
                {`\u2022`} {t('accordion.notification.point2')}
              </ThemedText>
              <ThemedText className="font-semibold">{t('accordion.forfeiting.title')}</ThemedText>
              <ThemedText className="ml-2">
                {`\u2022`} {t('accordion.forfeiting.point1')}
              </ThemedText>
            </View>
          }
        />
      </ScrollView>
    </View>
  );
}
