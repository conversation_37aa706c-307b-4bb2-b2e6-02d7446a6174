import React from 'react';

import { Icon } from '@rneui/themed';
import { TextInput as NativeTextInput, Platform, TextInputProps, View } from 'react-native';

import { clsx } from '@/common/clsx';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

type IconProps = {
  name: string;
  type?: string;
  size?: number;
  color?: string;
};

type Props = TextInputProps & {
  component?: React.ComponentType<any>;
  leftIcon?: IconProps;
};

export function TextInput({ component, leftIcon, ...textInputProps }: Props) {
  const Component = component ?? NativeTextInput;
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;
  const placeholderColor = colors.inputPlaceholderColor;
  const textColor = colors.text;
  const backgroundColor = colors.inputBackground;
  const borderColor = colors.border;

  const inputStyle = {
    backgroundColor,
    color: textColor,
    borderColor,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: leftIcon ? 40 : 16,
    paddingVertical: 8,
    fontSize: 16,
  };

  if (!leftIcon) {
    return (
      <Component
        placeholderTextColor={placeholderColor}
        {...textInputProps}
        style={[inputStyle, Platform.OS === 'ios' && { height: 48 }, textInputProps.style]}
        className={clsx(textInputProps.className)}
      />
    );
  }

  return (
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor,
        borderColor,
        borderWidth: 1,
        borderRadius: 12,
        paddingHorizontal: 12,
        paddingVertical: 8,
        ...(Platform.OS === 'ios' && { height: 48 }),
      }}
    >
      <Icon
        name={leftIcon.name}
        type={leftIcon.type || 'material'}
        size={leftIcon.size || 20}
        color={leftIcon.color || colors.inputIconColor}
        style={{ marginRight: 8 }}
      />
      <Component
        placeholderTextColor={placeholderColor}
        {...textInputProps}
        style={[
          {
            flex: 1,
            color: textColor,
            fontSize: 16,
            backgroundColor: 'transparent',
            borderWidth: 0,
            paddingHorizontal: 0,
            paddingVertical: 0,
          },
          textInputProps.style,
        ]}
        className={clsx(textInputProps.className)}
      />
    </View>
  );
}
