import React from 'react';

import { TextInput as NativeTextInput, Platform, TextInputProps } from 'react-native';

import { clsx } from '@/common/clsx';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

type Props = TextInputProps & { component?: React.ComponentType<any> };

export function TextInput({ component, ...textInputProps }: Props) {
  const Component = component ?? NativeTextInput;
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;
  const placeholderColor = colors.tabIconDefault;
  const textColor = colors.text;
  const backgroundColor = colors.secondary;
  const borderColor = colors.border;

  return (
    <Component
      placeholderTextColor={placeholderColor}
      {...textInputProps}
      style={[
        {
          backgroundColor,
          color: textColor,
          borderColor,
          borderWidth: 1,
          borderRadius: 12,
          paddingHorizontal: 16,
          paddingVertical: 8,
          fontSize: 16,
        },
        Platform.OS === 'ios' && { height: 48 },
        textInputProps.style,
      ]}
      className={clsx(textInputProps.className)}
    />
  );
}
