import React, { useCallback, useMemo, useState } from 'react';

import { BottomSheetTextInput } from '@gorhom/bottom-sheet';
import { Icon } from '@rneui/themed';
import { capitalize } from 'lodash';
import { useTranslation } from 'react-i18next';
import { Dimensions, Keyboard, StyleProp, TouchableOpacity, View, ViewStyle } from 'react-native';
import { FlatList } from 'react-native-gesture-handler';

import { clsx } from '@/common/clsx';
import { ThemedText } from '@/components/global/ThemedText';
import { showCustom } from '@/components/global/prompt';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

import { TextInput } from './TextInput';

export interface SelectableItem {
  [key: string]: any; // Allow additional properties
}

interface SelectModalProps<T extends SelectableItem> {
  items: T[];
  selectedItems?: T[];
  onSelect: (selectedItems: T[]) => void;
  multiple?: boolean;
  label?: string;
  placeholder?: string;
  searchPlaceholder?: string;
  modalTitle?: string;
  noResultsText?: string;
  style?: StyleProp<ViewStyle>;
  inputClassName?: string;
  disabled?: boolean;
  renderItem?: (item: T, isSelected: boolean, onPress: () => void) => React.JSX.Element;
  keyExtractor?: (item: T) => string;
  labelExtractor?: (item: T) => string;
}

export function SelectModal<T extends SelectableItem>({
  items,
  selectedItems = [],
  onSelect,
  multiple = true,
  label,
  placeholder,
  searchPlaceholder,
  modalTitle,
  noResultsText,
  style,
  inputClassName,
  disabled = false,
  renderItem,
  keyExtractor = item => item.id,
  labelExtractor = item => item.label,
}: SelectModalProps<T>) {
  const { t } = useTranslation();
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;
  const isLightTheme = currentTheme.startsWith('light');

  placeholder = placeholder ?? t('common.select.placeholder');
  searchPlaceholder = searchPlaceholder ?? t('common.select.searchPlaceholder');
  modalTitle = modalTitle ?? t('common.select.modalTitle');
  noResultsText = noResultsText ?? t('common.select.noResultsText');

  const displayLabel = useMemo(() => {
    if (label) return label;
    if (selectedItems.length === 0) return placeholder;
    if (selectedItems.length === 1) return labelExtractor(selectedItems[0]);
    return `${labelExtractor(selectedItems[0])} +${selectedItems.length - 1}`;
  }, [label, selectedItems, placeholder, labelExtractor]);

  const handleOpenSelector = useCallback(() => {
    if (disabled) return;

    showCustom(({ close }: any) => {
      const [search, setSearch] = useState('');
      const [selected, setSelected] = useState<T[]>(selectedItems);

      const filteredItems = useMemo(() => {
        if (!search) return items;
        const searchLower = search.toLowerCase();
        return items.filter(item => String(labelExtractor(item)).toLowerCase().includes(searchLower));
      }, [search, items]);

      const handleSelect = (item: T) => {
        if (!multiple) {
          // For single selection, immediately apply and close
          onSelect([item]);
          close();
          return;
        }

        // For multiple selection, update selection and apply immediately
        const newSelected = selected.some(selectedItem => keyExtractor(selectedItem) === keyExtractor(item))
          ? selected.filter(selectedItem => keyExtractor(selectedItem) !== keyExtractor(item))
          : [...selected, item];

        setSelected(newSelected);
        onSelect(newSelected);
      };

      const isSelected = (item: T) => {
        return selected.some(selectedItem => keyExtractor(selectedItem) === keyExtractor(item));
      };

      const defaultRenderItem = (item: T, isSelected: boolean) => (
        <TouchableOpacity onPress={() => handleSelect(item)} className={clsx('flex-row items-center py-3 px-0 mb-2')}>
          {/* Left side - Checkbox and Icon */}
          <View className="flex-row items-center mr-3">
            <Icon
              name={isSelected ? 'check-circle' : 'radio-button-unchecked'}
              type="material"
              size={20}
              color={isSelected ? colors.primary : colors.tabIconDefault}
              style={{ marginRight: 12 }}
            />
            {/* Portfolio Icon - using a colored circle with first letter */}
            <View
              className="w-10 h-10 rounded-lg items-center justify-center mr-3"
              style={{ backgroundColor: isSelected ? colors.primary : colors.secondary }}
            >
              <ThemedText className="text-white font-bold text-sm">
                {String(labelExtractor(item)).charAt(0).toUpperCase()}
              </ThemedText>
            </View>
          </View>

          {/* Middle - Portfolio Info */}
          <View className="flex-1">
            <ThemedText className="font-medium text-base mb-1">{String(labelExtractor(item))}</ThemedText>
            <ThemedText className="text-gray-500 text-sm">
              {(item as any).number || (item as any).id || 'Portfolio'}
            </ThemedText>
          </View>

          {/* Right side - Arrow */}
          <Icon name="chevron-right" type="material" size={20} color={colors.tabIconDefault} />
        </TouchableOpacity>
      );

      const handleSelectAll = () => {
        if (selected.length === items.length) {
          setSelected([]);
        } else {
          setSelected(items);
        }
      };

      return (
        <>
          {/* Custom Header */}
          <View className="flex-row items-center justify-between mb-5">
            <ThemedText h2 className="font-bold">
              {modalTitle}
            </ThemedText>
            <TouchableOpacity>
              <Icon name="add-circle-outline" type="material" size={24} color={colors.icon} />
            </TouchableOpacity>
          </View>

          {/* Search Input */}
          <View className="relative mb-4">
            <Icon
              name="search"
              type="material"
              size={20}
              color={colors.inputIconColor}
              style={{ position: 'absolute', left: 12, top: 16, zIndex: 1 }}
            />
            <TextInput
              component={BottomSheetTextInput}
              placeholder={searchPlaceholder}
              value={search}
              onChangeText={setSearch}
              className="pl-10"
              autoCapitalize="none"
              autoCorrect={false}
              clearButtonMode="always"
              onSubmitEditing={() => Keyboard.dismiss()}
            />
          </View>

          {/* Select All Link */}
          <View className="flex-row justify-end mb-3">
            <TouchableOpacity onPress={handleSelectAll}>
              <ThemedText className="text-blue-500 font-medium">
                {selected.length === items.length ? 'Deselect all' : 'Select all'}
              </ThemedText>
            </TouchableOpacity>
          </View>

          <FlatList
            data={filteredItems}
            keyExtractor={keyExtractor}
            style={{ maxHeight: Dimensions.get('window').height * 0.9, marginBottom: 10 }}
            contentContainerStyle={{ paddingVertical: 2 }}
            className="-mr-2.5 pr-2.5"
            renderItem={({ item }) =>
              renderItem
                ? renderItem(item, isSelected(item), () => handleSelect(item))
                : defaultRenderItem(item, isSelected(item))
            }
            ListEmptyComponent={() => (
              <View className="py-6">
                <ThemedText className="text-center text-gray-500">
                  {items.length === 0 ? 'No portfolios exist' : noResultsText}
                </ThemedText>
              </View>
            )}
          />
        </>
      );
    });
  }, [
    items,
    selectedItems,
    multiple,
    disabled,
    onSelect,
    searchPlaceholder,
    modalTitle,
    noResultsText,
    renderItem,
    keyExtractor,
    labelExtractor,
  ]);

  return (
    <TouchableOpacity
      onPress={handleOpenSelector}
      disabled={disabled}
      style={[
        {
          backgroundColor: isLightTheme ? '#FFFFFF' : '#000000',
          paddingHorizontal: 16,
          height: 48,
          borderRadius: 24,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          opacity: disabled ? 0.5 : 1,
        },
        style,
      ]}
    >
      <Icon name="menu" type="material" size={20} color={isLightTheme ? '#000' : '#fff'} style={{ marginRight: 8 }} />
      <ThemedText
        numberOfLines={1}
        size={16}
        type={selectedItems.length > 0 ? 'semi-bold' : 'regular'}
        lightColor="#000"
        darkColor="#fff"
        className={clsx('flex-1', inputClassName)}
      >
        {capitalize(displayLabel)}
      </ThemedText>
    </TouchableOpacity>
  );
}
