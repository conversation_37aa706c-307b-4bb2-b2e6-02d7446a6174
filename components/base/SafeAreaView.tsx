import React from 'react';

import { SafeAreaView as NativeSafeAreaView, SafeAreaViewProps } from 'react-native-safe-area-context';

import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

import { ThemedView } from '../global/ThemedView';

type Props = SafeAreaViewProps;

export function SafeAreaView({ children, style, ...props }: Props) {
  // Theme is now handled by ThemedView

  return (
    <NativeSafeAreaView
      {...props}
      style={[
        {
          flex: 1,
        },
        style,
      ]}
      edges={['bottom']}
    >
      <ThemedView style={{ flex: 1 }}>{children}</ThemedView>
    </NativeSafeAreaView>
  );
}
