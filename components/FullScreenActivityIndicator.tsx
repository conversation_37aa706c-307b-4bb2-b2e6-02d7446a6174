import { ActivityIndicator, View } from 'react-native';

import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

export default function FullScreenActivityIndicator() {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: colors.background }}>
      <ActivityIndicator size="small" color={colors.text} />
    </View>
  );
}
