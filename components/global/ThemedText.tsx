import { Platform, StyleSheet, Text, type TextProps } from 'react-native';
import Animated from 'react-native-reanimated';

import { clsx } from '@/common/clsx';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';
import { scaleFont } from '@/utils/scaler';

export type ThemedTextProps = TextProps & {
  lightColor?: string;
  darkColor?: string;
  type?: 'regular' | 'semi-bold' | 'bold' | 'outfit' | 'outfit-semi-bold' | 'outfit-bold';
  size?: number;
  // Additional props from the base Text component
  className?: string;
  textType?: 'default' | 'muted' | 'success' | 'error' | 'danger' | 'info';
  h1?: boolean;
  h2?: boolean;
  h3?: boolean;
  h4?: boolean;
  animated?: boolean;
};

export function ThemedText({
  style,
  lightColor,
  darkColor,
  type = 'regular',
  size,
  className,
  textType = 'default',
  h1,
  h2,
  h3,
  h4,
  animated,
  ...rest
}: ThemedTextProps) {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;
  const isLightTheme = currentTheme.startsWith('light');

  // Choose the appropriate Text component
  const TextComponent = animated ? Animated.Text : Text;

  // Define colors based on textType and custom colors
  const getTextColor = () => {
    // Handle custom light/dark colors
    if (lightColor || darkColor) {
      return isLightTheme ? lightColor || colors.text : darkColor || colors.text;
    }

    switch (textType) {
      case 'muted':
        return colors.tabIconDefault;
      case 'success':
        return '#22c55e'; // green-500
      case 'error':
      case 'danger':
        return '#ef4444'; // red-500
      case 'info':
        return '#3b82f6'; // blue-500
      default:
        return colors.text;
    }
  };

  // Calculate font size based on h1-h4 props or size prop
  const getFontSize = () => {
    if (h1) return Platform.OS === 'web' ? 28 : scaleFont(28);
    if (h2) return Platform.OS === 'web' ? 24 : scaleFont(24);
    if (h3) return Platform.OS === 'web' ? 20 : scaleFont(20);
    if (h4) return Platform.OS === 'web' ? 16 : scaleFont(16);
    if (size) return Platform.OS === 'web' ? size : scaleFont(size);
    return Platform.OS === 'web' ? 16 : scaleFont(14);
  };

  // Calculate font weight based on h1-h4 props
  const getFontWeight = () => {
    if (h1) return 'bold';
    if (h2) return '600';
    if (h3 || h4) return '500';
    return 'normal';
  };

  return (
    <TextComponent
      style={[
        { color: getTextColor() },
        type === 'regular' ? styles.default : undefined,
        type === 'semi-bold' ? styles.defaultSemiBold : undefined,
        type === 'bold' ? styles.defaultBold : undefined,
        type === 'outfit' ? styles.defaultOutfit : undefined,
        type === 'outfit-semi-bold' ? styles.defaultOutfitSemiBold : undefined,
        type === 'outfit-bold' ? styles.defaultOutfitBold : undefined,
        {
          fontSize: getFontSize(),
          fontWeight: getFontWeight(),
        },
        style,
      ]}
      className={clsx(className)}
      {...rest}
    />
  );
}

const styles = StyleSheet.create({
  default: {
    fontFamily: 'TintRegular',
  },
  defaultSemiBold: {
    fontFamily: 'TintSemiBold',
  },
  defaultBold: {
    fontFamily: 'TintBold',
  },
  defaultOutfit: {
    fontFamily: 'OutfitRegular',
  },
  defaultOutfitSemiBold: {
    fontFamily: 'OutfitSemiBold',
  },
  defaultOutfitBold: {
    fontFamily: 'OutfitBold',
  },
});
