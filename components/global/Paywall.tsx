import React from 'react';

import { Icon } from '@rneui/themed';
import { useFocusEffect } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { Pressable, StyleSheet, View } from 'react-native';

import { ThemedText } from '@/components/global/ThemedText';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';
import { isPaywallPressed, isPaywallVisible } from '@/signals/app.signal';

import SubscriptionModal from '../features/subscription/SubscriptionModal';
import { ModalManager } from './modal';

export default function Paywall() {
  const handlePress = () => {
    if (!!isPaywallPressed.value) return;
    isPaywallPressed.value = true;
    ModalManager.showModal(SubscriptionModal);
    // Reset after a short delay
    setTimeout(() => (isPaywallPressed.value = false), 500);
  };

  useFocusEffect(() => {
    isPaywallVisible.value = true;

    return () => {
      isPaywallVisible.value = false;
    };
  });

  return (
    <Pressable
      onPress={handlePress}
      disabled={isPaywallPressed.value}
      style={{
        ...StyleSheet.absoluteFillObject,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.1)',
        justifyContent: 'flex-end',
        alignItems: 'center',
      }}
    />
  );
}

export function PaywallBottomTab() {
  const { t } = useTranslation();
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;

  const handlePress = () => {
    if (!!isPaywallPressed.value) return;
    isPaywallPressed.value = true;
    ModalManager.showModal(SubscriptionModal);
    // Reset after a short delay
    setTimeout(() => (isPaywallPressed.value = false), 500);
  };

  return (
    <Pressable
      onPress={handlePress}
      style={{
        backgroundColor: colors.background,
        width: '100%',
        paddingVertical: 12,
        paddingHorizontal: 20,
        shadowColor: 'rgba(0,0,0,0.1)',
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 1,
        shadowRadius: 4,
        elevation: 4,
      }}
    >
      <View className="flex-row justify-between">
        <ThemedText h4 type="semi-bold" className="max-w-[90%]">
          {t('subscription.paywall.reservedForMembers')}
        </ThemedText>
        <Icon type="material-community" name="lock-outline" color={colors.primary} />
      </View>
      <ThemedText textType="muted" size={14}>
        {t('subscription.paywall.pressToStart')}
      </ThemedText>
    </Pressable>
  );
}
