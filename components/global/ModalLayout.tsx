import React from 'react';

import { Header, Icon } from '@rneui/themed';
import { Platform, View } from 'react-native';
import { TouchableOpacity } from 'react-native';
import { ScrollView } from 'react-native';

import { clsx } from '@/common/clsx';
import { ThemedText } from '@/components/global/ThemedText';
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

type Props = { dismiss: () => void; children: React.ReactNode; title: string; noScrollView?: boolean };

export default function ModalLayout({ dismiss, children, title, noScrollView = false }: Props) {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;
  return (
    <View style={{ flex: 1, backgroundColor: colors.background }} className={clsx(Platform.OS === 'ios' && 'pb-5')}>
      <Header
        backgroundColor={colors.background}
        centerComponent={
          <View className="flex-1 flex-row items-center">
            <ThemedText size={16} type="bold" className="text-center">
              {title}
            </ThemedText>
          </View>
        }
        rightComponent={
          <TouchableOpacity onPress={dismiss}>
            <View className="rounded-2xl p-1 m-[5px]" style={{ backgroundColor: colors.secondary }}>
              <Icon name="close" size={16} color={colors.tabIconDefault} />
            </View>
          </TouchableOpacity>
        }
        containerStyle={{ borderBottomWidth: 0 }}
      />

      {!noScrollView ? (
        <ScrollView>
          <View className="p-5">{children}</View>
        </ScrollView>
      ) : (
        <View className="flex-1">{children}</View>
      )}
    </View>
  );
}
