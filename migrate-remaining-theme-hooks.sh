#!/bin/bash

# Script to migrate remaining files from deprecated theme hooks to new theme context
# This script will help identify and update the remaining files

echo "🔍 Finding remaining files with deprecated theme hooks..."

# Find all files that still use deprecated hooks
files=$(find . -name "*.tsx" -o -name "*.ts" | xargs grep -l "useThemeColorLegacy\|useCurrentThemeColors\|useThemeColorDirect\|useThemeLinearGradient" | grep -v node_modules | grep -v hooks/useThemeColor.ts)

echo "📊 Found $(echo "$files" | wc -l) files still using deprecated hooks:"
echo "$files"

echo ""
echo "🔧 For each file, you need to:"
echo "1. Replace import: import { useThemeColorLegacy } from '@/hooks/useThemeColor';"
echo "   With: import { Colors } from '@/constants/Colors'; import { useTheme } from '@/context/ThemeContext';"
echo ""
echo "2. Replace hook usage: const theme = useThemeColorLegacy();"
echo "   With: const { currentTheme } = useTheme(); const colors = Colors[currentTheme] || Colors.lightBlue;"
echo ""
echo "3. Replace theme property references:"
echo "   - theme.theme → colors.primary"
echo "   - theme.text → colors.text"
echo "   - theme.backgroundPrimary → colors.background"
echo "   - theme.backgroundSecondary → colors.secondary"
echo "   - theme.muted → colors.tabIconDefault"
echo "   - theme.tint → colors.primary"
echo "   - theme.icon → colors.icon"
echo "   - theme.border → colors.border"
echo "   - theme.tabIconDefault → colors.tabIconDefault"
echo "   - theme.style === 'light' → currentTheme.startsWith('light')"
echo ""
echo "4. Update ThemedText components:"
echo "   - Move fontSize from style to size prop"
echo "   - Move fontWeight from style to type prop (bold/semi-bold/regular)"
echo ""

echo "🎯 Priority files to update first:"
echo "- App pages and layouts"
echo "- Core components (modals, prompts, etc.)"
echo "- Feature components"
echo ""

echo "✅ Files already updated:"
echo "- ThemedText, ThemedView, Button, TextInput, SelectModal"
echo "- FullScreenActivityIndicator, ModalLayout, Paywall, Snackbar"
echo "- Widget, CompanyQuotesWidget, SimulationWidget"
echo "- DepotLoading, ChooseDepots, SafeAreaView, CopyableString"
echo "- BlurredHeader, PortfolioSelector"
echo "- All layout files (_layout.tsx)"
echo "- ThemeSelector, ThemedCard, ValueDivisionExampleWidget"
