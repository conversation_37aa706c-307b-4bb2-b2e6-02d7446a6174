/**
 * DEPRECATED: These hooks are deprecated in favor of using the theme context directly.
 *
 * Migration guide:
 * - Instead of useThemeColor, useThemeColorDirect, useCurrentThemeColors, useThemeLinearGradient
 * - Use: const { currentTheme } = useTheme(); const colors = Colors[currentTheme] || Colors.lightBlue;
 *
 * Learn more about light and dark modes:
 * https://docs.expo.dev/guides/color-schemes/
 */
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

type ColorName = Exclude<keyof typeof Colors.lightBlue, 'linearGradient'>;

/**
 * @deprecated Use theme context directly: const { currentTheme } = useTheme(); const colors = Colors[currentTheme];
 */
export function useThemeColor(props: { light?: string; dark?: string } = {}, colorName: ColorName) {
  console.warn('useThemeColor is deprecated. Use theme context directly.');
  const { currentTheme } = useTheme();

  // Safety check for currentTheme
  if (!currentTheme || !Colors[currentTheme]) {
    console.warn('useThemeColor: currentTheme is invalid, falling back to lightBlue');
    const fallbackTheme = 'lightBlue';
    const isLightTheme = true;
    const themeType = isLightTheme ? 'light' : 'dark';
    const colorFromProps = props[themeType];

    if (colorFromProps) {
      return colorFromProps;
    }
    return Colors[fallbackTheme][colorName];
  }

  // Determine if current theme is light or dark for props fallback
  const isLightTheme = currentTheme.startsWith('light');
  const themeType = isLightTheme ? 'light' : 'dark';
  const colorFromProps = props[themeType];

  if (colorFromProps) {
    return colorFromProps;
  } else {
    // Use the specific theme colors
    return Colors[currentTheme][colorName];
  }
}

/**
 * @deprecated Use theme context directly: const { currentTheme } = useTheme(); const colors = Colors[currentTheme];
 */
export function useCurrentThemeColors() {
  console.warn('useCurrentThemeColors is deprecated. Use theme context directly.');
  const { currentTheme } = useTheme();

  // Safety check for currentTheme
  if (!currentTheme || !Colors[currentTheme]) {
    console.warn('useCurrentThemeColors: currentTheme is invalid, falling back to lightBlue');
    return Colors.lightBlue;
  }

  return Colors[currentTheme];
}

/**
 * @deprecated Use theme context directly: const { currentTheme } = useTheme(); const colors = Colors[currentTheme]; colors.colorName
 */
export function useThemeColorDirect(colorName: ColorName) {
  console.warn('useThemeColorDirect is deprecated. Use theme context directly.');
  const { currentTheme } = useTheme();

  // Safety check for currentTheme
  if (!currentTheme || !Colors[currentTheme]) {
    console.warn('useThemeColorDirect: currentTheme is invalid, falling back to lightBlue');
    return Colors.lightBlue[colorName];
  }

  return Colors[currentTheme][colorName];
}

/**
 * @deprecated Use theme context directly: const { currentTheme } = useTheme(); const colors = Colors[currentTheme]; colors.linearGradient
 */
export function useThemeLinearGradient() {
  console.warn('useThemeLinearGradient is deprecated. Use theme context directly.');
  const { currentTheme } = useTheme();

  // Safety check for currentTheme
  if (!currentTheme || !Colors[currentTheme]) {
    console.warn('useThemeLinearGradient: currentTheme is invalid, falling back to lightBlue');
    return Colors.lightBlue.linearGradient;
  }

  return Colors[currentTheme].linearGradient;
}

/**
 * @deprecated Use theme context directly: const { currentTheme } = useTheme(); const colors = Colors[currentTheme];
 */
export function useThemeColorLegacy() {
  console.warn('useThemeColorLegacy is deprecated. Use theme context directly.');
  const { currentTheme, setTheme } = useTheme();
  const colors = Colors[currentTheme] || Colors.lightBlue;

  // Return object that matches the old API
  return {
    theme: colors.primary,
    text: colors.text,
    backgroundPrimary: colors.background,
    backgroundSecondary: colors.secondary,
    muted: colors.tabIconDefault,
    tint: colors.primary,
    icon: colors.icon,
    border: colors.border,
    tabIconDefault: colors.tabIconDefault,
    style: currentTheme.startsWith('light') ? 'light' : 'dark',
    allColors: {
      light: colors,
      dark: colors,
    },
    toggleTheme: () => {
      // Simple toggle between light and dark variants of current theme
      if (currentTheme.startsWith('light')) {
        const darkVariant = currentTheme.replace('light', 'dark') as any;
        setTheme(darkVariant);
      } else if (currentTheme.startsWith('dark')) {
        const lightVariant = currentTheme.replace('dark', 'light') as any;
        setTheme(lightVariant);
      }
    },
  };
}
