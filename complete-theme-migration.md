# Complete Theme Migration Guide

## ✅ Progress So Far

We've successfully migrated **major components** from deprecated theme hooks to the new theme context:

### Core Components Updated:

- ✅ ThemedText, ThemedView, Button, TextInput, SelectModal
- ✅ FullScreenActivityIndicator, ModalLayout, Paywall, Snackbar
- ✅ SafeAreaView, CopyableString, ComingSoonScreen, SectionList
- ✅ BlurredHeader, PortfolioSelector, ThemeSelector
- ✅ Widget, CompanyQuotesWidget, SimulationWidget, CompanyHeader
- ✅ DepotLoading, ChooseDepots, ValueDivisionExampleWidget
- ✅ SubscriptionModal, prompt component
- ✅ All layout files (\_layout.tsx)

## 🔧 Remaining Files (29 files)

Run this command to see remaining files:

```bash
find . -name "*.tsx" -o -name "*.ts" | xargs grep -l "useThemeColorLegacy\|useCurrentThemeColors\|useThemeColorDirect\|useThemeLinearGradient" | grep -v node_modules | grep -v hooks/useThemeColor.ts
```

## 📋 Migration Pattern for Each File

### 1. Replace Import Statement

**OLD:**

```typescript
import { useThemeColorLegacy } from '@/hooks/useThemeColor';
// or
import { useCurrentThemeColors, useThemeColorDirect, useThemeLinearGradient } from '@/hooks/useThemeColor';
```

**NEW:**

```typescript
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';
```

### 2. Replace Hook Usage

**OLD:**

```typescript
const theme = useThemeColorLegacy();
const colors = useCurrentThemeColors();
const primaryColor = useThemeColorDirect('primary');
const gradientColors = useThemeLinearGradient();
```

**NEW:**

```typescript
const { currentTheme } = useTheme();
const colors = Colors[currentTheme] || Colors.lightBlue;
const isLightTheme = currentTheme.startsWith('light'); // if needed
```

### 3. Replace Theme Property References

| Old Property                | New Property                       |
| --------------------------- | ---------------------------------- |
| `theme.theme`               | `colors.primary`                   |
| `theme.text`                | `colors.text`                      |
| `theme.backgroundPrimary`   | `colors.background`                |
| `theme.backgroundSecondary` | `colors.secondary`                 |
| `theme.muted`               | `colors.tabIconDefault`            |
| `theme.tint`                | `colors.primary`                   |
| `theme.icon`                | `colors.icon`                      |
| `theme.border`              | `colors.border`                    |
| `theme.tabIconDefault`      | `colors.tabIconDefault`            |
| `theme.style === 'light'`   | `currentTheme.startsWith('light')` |

### 4. Update ThemedText Components

**OLD:**

```typescript
<ThemedText className="font-bold text-lg" style={{ fontSize: 16, fontWeight: 'bold' }}>
```

**NEW:**

```typescript
<ThemedText size={16} type="bold">
```

## 🎯 Priority Order for Remaining Files

1. **App pages and screens** - Most visible to users
2. **Feature components** - Core functionality
3. **Utility components** - Supporting components
4. **Example/test components** - Lowest priority

## ⚠️ Important Notes

- Always test the component after migration
- Check for any `theme.` references that might be missed
- Ensure proper color contrast in both light and dark themes
- Update any hardcoded theme checks to use `currentTheme.startsWith('light')`

## 🚀 Final Step

Once all files are migrated, we can completely remove the deprecated hooks from `hooks/useThemeColor.ts` or mark them as completely deprecated.
